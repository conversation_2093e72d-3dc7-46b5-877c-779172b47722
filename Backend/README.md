# Biormika Backend API

FastAPI backend for the Biormika EDF file upload and management system.

## Features

- Generate presigned URLs for secure file uploads to S3
- List all uploaded EDF files
- Delete files from S3
- Generate download URLs for files
- File validation (only .edf files, max 1GB)

## Prerequisites

- Python 3.8 or higher
- AWS credentials configured with `biormika` profile
- S3 bucket created (from CDK infrastructure)

## Setup

1. Create and activate virtual environment:
```bash
python3 -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
```

2. Install dependencies:
```bash
pip install -r requirements.txt
```

3. Configure environment:
- Copy `.env.example` to `.env`
- Update `S3_BUCKET_NAME` with your actual bucket name from CDK deployment

## Running the Server

```bash
python run.py
```

The API will be available at `http://localhost:8000`

## API Documentation

Once the server is running, you can access:
- Interactive API docs: `http://localhost:8000/docs`
- ReDoc documentation: `http://localhost:8000/redoc`

## API Endpoints

### Generate Presigned URL
- **POST** `/api/v1/files/generate-presigned-url`
- Request body: `{"filename": "test.edf", "filesize": 1000000}`
- Returns presigned URL for uploading

### List Files
- **GET** `/api/v1/files/list`
- Returns list of all EDF files in S3

### Delete File
- **DELETE** `/api/v1/files/delete`
- Request body: `{"key": "edf-files/..."}`
- Deletes specified file from S3

### Get Download URL
- **GET** `/api/v1/files/download/{key}`
- Returns presigned URL for downloading

## Error Handling

The API includes comprehensive error handling:
- File type validation (only .edf files)
- File size validation (max 1GB)
- Proper HTTP status codes
- Detailed error messages
