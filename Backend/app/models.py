from pydantic import BaseModel, Field, validator
from typing import Optional, List
from datetime import datetime


class PresignedUrlRequest(BaseModel):
    filename: str = Field(..., description="Name of the file to upload")
    filesize: int = Field(..., description="Size of the file in bytes")

    @validator("filename")
    def validate_filename(cls, v):
        if not v.lower().endswith((".edf", ".EDF")):
            raise ValueError("Only .edf files are allowed")
        return v

    @validator("filesize")
    def validate_filesize(cls, v):
        max_size = 1024 * 1024 * 1024  # 1GB in bytes
        if v > max_size:
            raise ValueError(f"File too large. Maximum size is 1GB")
        if v <= 0:
            raise ValueError("File size must be greater than 0")
        return v


class PresignedUrlResponse(BaseModel):
    url: str = Field(..., description="Presigned URL for file upload")
    key: str = Field(..., description="S3 object key")
    expires_in: int = Field(..., description="URL expiry time in seconds")


class FileInfo(BaseModel):
    key: str = Field(..., description="S3 object key")
    filename: str = Field(..., description="Original filename")
    size: int = Field(..., description="File size in bytes")
    last_modified: datetime = Field(...,
                                    description="Last modification timestamp")
    etag: Optional[str] = Field(None, description="ETag of the object")

    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class FileListResponse(BaseModel):
    files: List[FileInfo] = Field(...,
                                  description="List of files in the bucket")
    total_count: int = Field(..., description="Total number of files")
    total_size: int = Field(...,
                            description="Total size of all files in bytes")


class FileDeleteRequest(BaseModel):
    key: str = Field(..., description="S3 object key to delete")


class FileDeleteResponse(BaseModel):
    success: bool = Field(..., description="Whether deletion was successful")
    message: str = Field(..., description="Status message")
    deleted_key: str = Field(..., description="Key of the deleted object")


class ErrorResponse(BaseModel):
    error: str = Field(..., description="Error type")
    message: str = Field(..., description="Error message")
    detail: Optional[str] = Field(None, description="Additional error details")
