from fastapi import APIRouter, HTTPException, status
from typing import Optional
import uuid
from datetime import datetime

from ..models import (
    PresignedUrlRequest,
    PresignedUrlResponse,
    FileListResponse,
    FileDeleteRequest,
    FileDeleteResponse,
    ErrorResponse
)
from ..services.s3_service import s3_service
from ..config import settings


router = APIRouter(prefix="/files", tags=["files"])


@router.post(
    "/generate-presigned-url",
    response_model=PresignedUrlResponse,
    responses={
        400: {"model": ErrorResponse, "description": "Invalid file type or size"},
        500: {"model": ErrorResponse, "description": "Server error"}
    }
)
async def generate_presigned_url(request: PresignedUrlRequest):
    """Generate a presigned URL for uploading an EDF file to S3"""
    try:
        # Generate unique key with timestamp
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        file_extension = request.filename.split(".")[-1]
        key = f"edf-files/{timestamp}_{uuid.uuid4().hex[:8]}_{request.filename}"

        # Generate presigned URL
        url = s3_service.generate_presigned_url(key, request.filename)

        return PresignedUrlResponse(
            url=url,
            key=key,
            expires_in=settings.presigned_url_expiry_seconds
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to generate presigned URL: {str(e)}"
        )


@router.get(
    "/list",
    response_model=FileListResponse,
    responses={
        500: {"model": ErrorResponse, "description": "Server error"}
    }
)
async def list_files(prefix: Optional[str] = None):
    """List all EDF files in the S3 bucket"""
    try:
        files = s3_service.list_files(prefix)

        total_size = sum(file.size for file in files)

        return FileListResponse(
            files=files,
            total_count=len(files),
            total_size=total_size
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to list files: {str(e)}"
        )


@router.delete(
    "/delete",
    response_model=FileDeleteResponse,
    responses={
        404: {"model": ErrorResponse, "description": "File not found"},
        500: {"model": ErrorResponse, "description": "Server error"}
    }
)
async def delete_file(request: FileDeleteRequest):
    """Delete an EDF file from S3"""
    try:
        # Check if file exists
        metadata = s3_service.get_file_metadata(request.key)
        if not metadata:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"File not found: {request.key}"
            )

        # Delete the file
        success = s3_service.delete_file(request.key)

        return FileDeleteResponse(
            success=success,
            message="File deleted successfully",
            deleted_key=request.key
        )
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to delete file: {str(e)}"
        )


@router.get(
    "/download/{key:path}",
    responses={
        200: {"description": "Download URL"},
        404: {"model": ErrorResponse, "description": "File not found"},
        500: {"model": ErrorResponse, "description": "Server error"}
    }
)
async def get_download_url(key: str):
    """Generate a presigned URL for downloading a file"""
    try:
        # Check if file exists
        metadata = s3_service.get_file_metadata(key)
        if not metadata:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"File not found: {key}"
            )

        # Generate download URL
        url = s3_service.generate_download_url(key)

        return {
            "download_url": url,
            "expires_in": 3600,
            "filename": key.split("/")[-1],
            "size": metadata["size"]
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to generate download URL: {str(e)}"
        )
