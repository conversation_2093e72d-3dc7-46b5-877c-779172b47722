# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

### Frontend (React + Vite + TypeScript)
```bash
cd Frontend
npm install          # Install dependencies
npm run dev          # Start dev server (http://localhost:5173)
npm run build        # Production build
npm run lint         # Run ESLint
npm run preview      # Preview production build
```

### Backend (FastAPI + Python)
```bash
cd Backend
pip install -r requirements.txt  # Install dependencies
python run.py                    # Start dev server (http://localhost:8000)
# API docs available at http://localhost:8000/docs
```

### Infrastructure (AWS CDK)
```bash
cd Infra
pip install -r requirements.txt           # Install CDK dependencies
cdk bootstrap --profile biormika          # First-time AWS setup
cdk deploy --profile biormika              # Deploy infrastructure
cdk destroy --profile biormika             # Cleanup AWS resources
```

## Architecture Overview

This is a **3-tier serverless EDF file management system** for neuroscience HFO analysis:

- **Frontend**: React 19 SPA with TypeScript, Vite bundler, Tailwind CSS styling
- **Backend**: FastAPI REST API with S3 integration for file storage
- **Infrastructure**: AWS CDK managing S3 bucket with security-first configuration

### Service Communication Flow
1. Frontend uploads EDF files via multipart form to Backend
2. Backend validates files (extension, size) and stores in S3
3. Backend generates presigned URLs for secure file access
4. Frontend displays file list and enables download/delete operations

## Critical Architectural Patterns

### Frontend Architecture
- **Component Structure**: Functional components with hooks in `/src/components/`
- **API Layer**: Centralized service layer in `/src/services/api.ts` using Axios
- **Type Safety**: Strict TypeScript with interfaces for all API responses
- **Styling**: Tailwind utility classes, avoid inline styles
- **State Management**: Currently using React useState (no Redux/Context)

### Backend Architecture
- **Clean Architecture Layers**:
  - `/routers/` - FastAPI route handlers (API endpoints)
  - `/services/` - Business logic (S3 operations, validations)
  - `/models/` - Pydantic models for request/response validation
  - `/config.py` - Environment configuration using Pydantic Settings
- **S3 Integration**: Singleton pattern for S3 client in services
- **Error Handling**: Global exception handler returning proper HTTP status codes
- **File Limits**: 1GB max file size, .edf extension validation

### Infrastructure Patterns
- **Security Configuration**:
  - S3 bucket blocks all public access
  - Server-side encryption enabled
  - Versioning enabled for data protection
  - CORS configured for frontend origin
- **IAM**: Least-privilege roles for backend S3 access
- **CDK Stack**: Single stack pattern with modular constructs

## Development Workflow

### Initial Setup
1. Deploy infrastructure: `cd Infra && cdk deploy --profile biormika`
2. Copy S3 bucket name from CDK output
3. Configure backend: Create `/Backend/.env` with `S3_BUCKET_NAME=<bucket-name>`
4. Start backend: `cd Backend && python run.py`
5. Start frontend: `cd Frontend && npm run dev`

### Environment Requirements
- **AWS Profile**: Must have `biormika` profile configured in AWS credentials
- **Python**: 3.11+ for backend and infrastructure
- **Node.js**: 18+ for frontend
- **Backend .env**: Must contain `S3_BUCKET_NAME` from CDK deployment

## Important Conventions

### Code Quality Standards
- **No Hardcoded Values**: Use constants at file top for all configuration values
- **Error Handling**: Always handle API errors with meaningful user messages
- **File Validation**: Enforce .edf extension and 1GB size limit consistently
- **TypeScript**: Maintain strict mode, define interfaces for all data structures
- **Component Reuse**: Check existing components before creating new ones

### API Response Patterns
- Success responses include data in `data` field
- Error responses include `detail` field with error message
- File operations return consistent `FileResponse` model structure
- Presigned URLs expire after 1 hour by default

### S3 Key Structure
- Files stored with pattern: `edf-files/{filename}`
- Maintain original filename for user recognition
- No nested folder structure currently implemented

## Known Limitations & Future Considerations
- No authentication/authorization system implemented
- No CI/CD pipeline configured
- Test coverage not yet implemented
- HFO analysis pipeline integration pending
- File chunking for large uploads not implemented