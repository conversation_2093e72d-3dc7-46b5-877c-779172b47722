import { useState } from "react";
import { FileUpload } from "./components/FileUpload";
import { FileList } from "./components/FileList";
import { FileIcon } from "lucide-react";
import "./App.css";

function App() {
  const [refreshTrigger, setRefreshTrigger] = useState(0);

  const handleUploadComplete = () => {
    setRefreshTrigger((prev) => prev + 1);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-6xl mx-auto px-4 py-4">
          <div className="flex items-center">
            <FileIcon className="w-8 h-8 text-blue-600 mr-3" />
            <h1 className="text-2xl font-bold text-gray-900">Biormika EDF File Manager</h1>
          </div>
          <p className="text-sm text-gray-600 mt-1">Upload and manage EDF files for HFO analysis</p>
        </div>
      </header>

      <main className="max-w-6xl mx-auto px-4 py-8">
        <section className="mb-12">
          <h2 className="text-xl font-semibold mb-6">Upload EDF File</h2>
          <FileUpload onUploadComplete={handleUploadComplete} />
        </section>

        <section>
          <FileList refreshTrigger={refreshTrigger} />
        </section>
      </main>

      <footer className="mt-16 pb-8 text-center text-sm text-gray-500">
        <p>Biormika &copy; 2024. EDF file processing system.</p>
      </footer>
    </div>
  );
}

export default App;
