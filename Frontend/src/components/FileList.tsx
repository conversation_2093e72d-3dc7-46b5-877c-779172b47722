import React, { useState, useEffect } from "react";
import { Download, Trash2, FileIcon, RefreshCw } from "lucide-react";
import { fileService } from "../services/api";
import type { FileInfo } from "../services/api";
import { formatFileSize, formatDate } from "../utils/file";

interface FileListProps {
  refreshTrigger?: number;
}

export const FileList: React.FC<FileListProps> = ({ refreshTrigger }) => {
  const [files, setFiles] = useState<FileInfo[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string>("");
  const [deletingKey, setDeletingKey] = useState<string | null>(null);

  const fetchFiles = async () => {
    try {
      setLoading(true);
      setError("");
      const response = await fileService.listFiles();
      setFiles(response.files);
    } catch {
      setError("Failed to load files");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchFiles();
  }, [refreshTrigger]);

  const handleDelete = async (key: string, filename: string) => {
    if (!confirm(`Are you sure you want to delete ${filename}?`)) {
      return;
    }

    try {
      setDeletingKey(key);
      await fileService.deleteFile(key);
      await fetchFiles();
    } catch {
      alert("Failed to delete file");
    } finally {
      setDeletingKey(null);
    }
  };

  const handleDownload = async (key: string) => {
    try {
      const { download_url } = await fileService.getDownloadUrl(key);
      window.open(download_url, "_blank");
    } catch {
      alert("Failed to generate download link");
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center p-8">
        <RefreshCw className="w-6 h-6 animate-spin text-gray-400" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center p-8">
        <p className="text-red-600 mb-4">{error}</p>
        <button onClick={fetchFiles} className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors">
          Retry
        </button>
      </div>
    );
  }

  if (files.length === 0) {
    return (
      <div className="text-center p-8">
        <FileIcon className="w-12 h-12 mx-auto mb-4 text-gray-300" />
        <p className="text-gray-500">No files uploaded yet</p>
      </div>
    );
  }

  return (
    <div className="w-full max-w-4xl mx-auto">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-xl font-semibold">Uploaded Files ({files.length})</h2>
        <button onClick={fetchFiles} className="p-2 hover:bg-gray-100 rounded transition-colors" title="Refresh">
          <RefreshCw className="w-4 h-4" />
        </button>
      </div>

      <div className="bg-white rounded-lg shadow overflow-hidden">
        <table className="w-full">
          <thead className="bg-gray-50 border-b">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">File Name</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Size</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Modified</th>
              <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
          </thead>
          <tbody className="divide-y divide-gray-200">
            {files.map((file) => (
              <tr key={file.key} className="hover:bg-gray-50">
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex items-center">
                    <FileIcon className="w-5 h-5 mr-2 text-gray-400" />
                    <span className="text-sm font-medium text-gray-900">{file.filename}</span>
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{formatFileSize(file.size)}</td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{formatDate(file.last_modified)}</td>
                <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                  <button onClick={() => handleDownload(file.key)} className="text-blue-600 hover:text-blue-900 mr-3" title="Download">
                    <Download className="w-4 h-4" />
                  </button>
                  <button
                    onClick={() => handleDelete(file.key, file.filename)}
                    disabled={deletingKey === file.key}
                    className="text-red-600 hover:text-red-900 disabled:opacity-50"
                    title="Delete"
                  >
                    {deletingKey === file.key ? <RefreshCw className="w-4 h-4 animate-spin" /> : <Trash2 className="w-4 h-4" />}
                  </button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};
