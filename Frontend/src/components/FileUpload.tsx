import React, { useState, useCallback } from "react";
import { Upload, FileIcon, AlertCircle, CheckCircle } from "lucide-react";
import { fileService } from "../services/api";
import { validateEDFFile, formatFileSize } from "../utils/file";

interface FileUploadProps {
  onUploadComplete?: () => void;
}

export const FileUpload: React.FC<FileUploadProps> = ({ onUploadComplete }) => {
  const [isDragging, setIsDragging] = useState(false);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [uploadStatus, setUploadStatus] = useState<"idle" | "uploading" | "success" | "error">("idle");
  const [errorMessage, setErrorMessage] = useState<string>("");

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);

    const files = Array.from(e.dataTransfer.files);
    if (files.length > 0) {
      handleFileSelect(files[0]);
    }
  }, []);

  const handleFileSelect = (file: File) => {
    const validation = validateEDFFile(file);

    if (!validation.valid) {
      setErrorMessage(validation.error || "Invalid file");
      setUploadStatus("error");
      return;
    }

    setSelectedFile(file);
    setErrorMessage("");
    setUploadStatus("idle");
  };

  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files && files.length > 0) {
      handleFileSelect(files[0]);
    }
  };

  const uploadFile = async () => {
    if (!selectedFile) return;

    try {
      setUploadStatus("uploading");
      setUploadProgress(0);

      // Get presigned URL
      const presignedUrlData = await fileService.getPresignedUrl({
        filename: selectedFile.name,
        filesize: selectedFile.size,
      });

      // Upload file to S3
      await fileService.uploadFile(selectedFile, presignedUrlData.url, (progress) => setUploadProgress(progress));

      setUploadStatus("success");
      setTimeout(() => {
        resetUpload();
        onUploadComplete?.();
      }, 2000);
    } catch (error) {
      setUploadStatus("error");
      setErrorMessage(error instanceof Error ? error.message : "Upload failed");
    }
  };

  const resetUpload = () => {
    setSelectedFile(null);
    setUploadProgress(0);
    setUploadStatus("idle");
    setErrorMessage("");
  };

  return (
    <div className="w-full max-w-2xl mx-auto p-6">
      <div
        className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors ${
          isDragging ? "border-blue-500 bg-blue-50" : "border-gray-300 hover:border-gray-400"
        }`}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
      >
        <input type="file" id="file-input" className="hidden" accept=".edf,.EDF" onChange={handleFileInputChange} />

        {!selectedFile ? (
          <label htmlFor="file-input" className="cursor-pointer">
            <Upload className="w-12 h-12 mx-auto mb-4 text-gray-400" />
            <p className="text-lg font-medium mb-2">Drag and drop your EDF file here</p>
            <p className="text-sm text-gray-500 mb-4">or click to browse</p>
            <p className="text-xs text-gray-400">Only .edf files up to 1GB are supported</p>
          </label>
        ) : (
          <div>
            <FileIcon className="w-12 h-12 mx-auto mb-4 text-blue-500" />
            <p className="text-lg font-medium mb-2">{selectedFile.name}</p>
            <p className="text-sm text-gray-500 mb-4">{formatFileSize(selectedFile.size)}</p>

            {uploadStatus === "idle" && (
              <div className="flex gap-2 justify-center">
                <button onClick={uploadFile} className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors">
                  Upload
                </button>
                <button onClick={resetUpload} className="px-4 py-2 bg-gray-300 text-gray-700 rounded hover:bg-gray-400 transition-colors">
                  Cancel
                </button>
              </div>
            )}

            {uploadStatus === "uploading" && (
              <div className="w-full">
                <div className="mb-2">Uploading... {uploadProgress}%</div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div className="bg-blue-500 h-2 rounded-full transition-all duration-300" style={{ width: `${uploadProgress}%` }} />
                </div>
              </div>
            )}

            {uploadStatus === "success" && (
              <div className="flex items-center justify-center text-green-600">
                <CheckCircle className="w-5 h-5 mr-2" />
                Upload successful!
              </div>
            )}
          </div>
        )}

        {uploadStatus === "error" && errorMessage && (
          <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-md flex items-center text-red-700">
            <AlertCircle className="w-5 h-5 mr-2 flex-shrink-0" />
            <span className="text-sm">{errorMessage}</span>
          </div>
        )}
      </div>
    </div>
  );
};
