export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return "0 Bytes";

  const k = 1024;
  const sizes = ["Bytes", "KB", "MB", "GB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
};

export const validateEDFFile = (file: File): { valid: boolean; error?: string } => {
  const maxSize = 1024 * 1024 * 1024; // 1GB
  const validExtensions = [".edf", ".EDF"];

  const fileName = file.name.toLowerCase();
  const hasValidExtension = validExtensions.some((ext) => fileName.endsWith(ext.toLowerCase()));

  if (!hasValidExtension) {
    return { valid: false, error: "Only .edf files are allowed" };
  }

  if (file.size > maxSize) {
    return { valid: false, error: "File size must not exceed 1GB" };
  }

  if (file.size === 0) {
    return { valid: false, error: "File cannot be empty" };
  }

  return { valid: true };
};

export const formatDate = (dateString: string): string => {
  const date = new Date(dateString);
  return date.toLocaleString("en-US", {
    year: "numeric",
    month: "short",
    day: "numeric",
    hour: "2-digit",
    minute: "2-digit",
  });
};
