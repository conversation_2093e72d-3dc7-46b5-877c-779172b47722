from aws_cdk import (
    Stack,
    Tags
)
from constructs import Construct
from .s3_construct import S3StorageConstruct


class InfraStack(Stack):
    def __init__(self, scope: Construct, construct_id: str, **kwargs) -> None:
        super().__init__(scope, construct_id, **kwargs)

        # Create S3 storage infrastructure
        s3_storage = S3StorageConstruct(
            self, "S3Storage",
            bucket_name=None,  # Let AWS generate unique name
            allowed_origins=[
                "http://localhost:5173",  # Vite dev server
                "http://localhost:3000",  # Alternative React port
                "http://localhost:8000"   # FastAPI backend
            ]
        )

        # Add tags to all resources
        Tags.of(self).add("Project", "Biormika")
        Tags.of(self).add("Environment", "Development")
        Tags.of(self).add("Purpose", "EDF-File-Processing")
