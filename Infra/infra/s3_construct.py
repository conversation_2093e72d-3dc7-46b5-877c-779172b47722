from aws_cdk import (
    aws_s3 as s3,
    aws_iam as iam,
    RemovalPolicy,
    Duration,
    CfnOutput
)
from constructs import Construct


class S3StorageConstruct(Construct):
    """Construct for S3 bucket to store EDF files with proper configurations"""

    def __init__(self, scope: Construct, id: str, *, bucket_name: str = None,
                 allowed_origins: list[str] = None) -> None:
        super().__init__(scope, id)

        # Create S3 bucket for EDF file storage
        self.edf_bucket = s3.Bucket(
            self, "EDFStorageBucket",
            bucket_name=bucket_name,  # Let AWS generate unique name if not provided
            versioned=True,  # Enable versioning for file recovery
            encryption=s3.BucketEncryption.S3_MANAGED,  # Server-side encryption
            block_public_access=s3.BlockPublicAccess.BLOCK_ALL,  # Block all public access
            lifecycle_rules=[
                s3.LifecycleRule(
                    id="DeleteIncompleteMultipartUploads",
                    abort_incomplete_multipart_upload_after=Duration.days(1)
                )
            ],
            cors=[
                s3.CorsRule(
                    allowed_methods=[
                        s3.HttpMethods.GET,
                        s3.HttpMethods.PUT,
                        s3.HttpMethods.POST,
                        s3.HttpMethods.HEAD
                    ],
                    allowed_origins=allowed_origins or [
                        "http://localhost:5173", "http://localhost:3000"  # Vite default ports
                    ],
                    allowed_headers=["*"],
                    exposed_headers=[
                        "ETag",
                        "x-amz-server-side-encryption",
                        "x-amz-request-id",
                        "x-amz-id-2"
                    ],
                    max_age=3600
                )
            ],
            removal_policy=RemovalPolicy.RETAIN  # Retain bucket on stack deletion for safety
        )

        # Create IAM role for backend service to access S3
        self.backend_role = iam.Role(
            self, "BackendServiceRole",
            # Can be changed to Lambda or ECS
            assumed_by=iam.ServicePrincipal("ec2.amazonaws.com"),
            description="Role for backend service to access S3 for EDF file operations"
        )

        # Grant backend role permissions to generate presigned URLs
        self.edf_bucket.grant_read_write(self.backend_role)

        # Create policy for presigned URL generation (more restrictive)
        presigned_url_policy = iam.PolicyStatement(
            effect=iam.Effect.ALLOW,
            actions=[
                "s3:GetObject",
                "s3:PutObject",
                "s3:DeleteObject",
                "s3:ListBucket"
            ],
            resources=[
                self.edf_bucket.bucket_arn,
                f"{self.edf_bucket.bucket_arn}/*"
            ],
            conditions={
                "StringLike": {
                    "s3:prefix": ["*.edf", "*.EDF"]
                }
            }
        )

        self.backend_role.add_to_policy(presigned_url_policy)

        # Output bucket name and ARN
        CfnOutput(
            self, "BucketName",
            value=self.edf_bucket.bucket_name,
            description="Name of the S3 bucket for EDF file storage"
        )

        CfnOutput(
            self, "BucketArn",
            value=self.edf_bucket.bucket_arn,
            description="ARN of the S3 bucket"
        )

        CfnOutput(
            self, "BackendRoleArn",
            value=self.backend_role.role_arn,
            description="ARN of the backend service role"
        )
