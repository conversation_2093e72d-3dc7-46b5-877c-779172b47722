# Biormika EDF File Upload and Management System

A full-stack application for uploading and managing EDF (European Data Format) files up to 1GB for future HFO (High Frequency Oscillation) analysis.

## Architecture Overview

- **Frontend**: React with TypeScript (Vite)
- **Backend**: FastAPI (Python)
- **Infrastructure**: AWS CDK with Python
- **Storage**: AWS S3 with presigned URLs for secure uploads

## Project Structure

```
biormika-stack/
├── Frontend/          # React TypeScript frontend
├── Backend/           # FastAPI Python backend
└── Infra/            # AWS CDK infrastructure
```

## Prerequisites

- Node.js 20.19+ (for Frontend)
- Python 3.8+ (for Backend and Infrastructure)
- AWS CLI configured with `biormika` profile
- AWS CDK CLI (`npm install -g aws-cdk`)

## Setup Instructions

### 1. Deploy AWS Infrastructure

```bash
cd Infra
python3 -m venv .venv
source .venv/bin/activate  # On Windows: .venv\Scripts\activate
pip install -r requirements.txt
cdk bootstrap --profile biormika  # First time only
cdk deploy --profile biormika
```

Note the S3 bucket name from the deployment output.

### 2. Configure and Run Backend

```bash
cd Backend
python3 -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
pip install -r requirements.txt
```

Create a `.env` file in the Backend directory:
```env
S3_BUCKET_NAME=your-bucket-name-from-cdk-output
```

Run the backend:
```bash
python run.py
```

The API will be available at `http://localhost:8000`
- API Documentation: `http://localhost:8000/docs`

### 3. Run Frontend

```bash
cd Frontend
npm install
npm run dev
```

The frontend will be available at `http://localhost:5173`

## Features

### File Upload
- Drag and drop or click to browse for EDF files
- File validation (only .edf files, max 1GB)
- Real-time upload progress tracking
- Secure upload using S3 presigned URLs

### File Management
- List all uploaded EDF files
- View file details (name, size, upload date)
- Download files with presigned URLs
- Delete files from S3

## API Endpoints

- `POST /api/v1/files/generate-presigned-url` - Get presigned URL for upload
- `GET /api/v1/files/list` - List all EDF files
- `DELETE /api/v1/files/delete` - Delete a file
- `GET /api/v1/files/download/{key}` - Get download URL

## Security Features

- S3 bucket with server-side encryption
- All public access blocked
- CORS configured for specific origins
- File type and size validation
- Presigned URLs with expiration

## Development

### Running Tests

Frontend:
```bash
cd Frontend
npm run lint
npm run build
```

### Building for Production

Frontend:
```bash
cd Frontend
npm run build
```

The built files will be in `Frontend/dist/`

## Future Enhancements

This system is designed as Phase 1 of a larger project. Future phases will include:
- HFO analysis processing pipeline
- Real-time analysis status tracking
- Results visualization
- Batch processing capabilities

## Troubleshooting

### Backend Connection Error
- Ensure the backend is running on port 8000
- Check CORS configuration matches frontend URL

### S3 Upload Failures
- Verify S3 bucket name in backend .env file
- Check AWS credentials and permissions
- Ensure bucket exists and is accessible

### File Size Limits
- Maximum file size is 1GB
- Ensure your S3 bucket has adequate storage
- Check network stability for large uploads

## License

Copyright 2024 Biormika. All rights reserved.
